"use client";

import { useState, useEffect, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  RefreshCw,
  Filter,
  Clock,
  UserCheck,
} from "lucide-react";
import { toast } from "sonner";
import { WorkingHoursManager } from "@/components/working-hours-manager";
import { AvailabilityManager } from "@/components/availability-manager";
import { AgentKanbanBoard } from "./agent-kanban-board";

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: "PENDING" | "ASSIGNED" | "IN_PROGRESS" | "COMPLETED" | "ESCALATED";
  type: string;
  assignedAt?: string;
  createdAt: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
}

interface Agent {
  id: string;
  name: string;
  email: string;
  status: "AVAILABLE" | "BUSY" | "AWAY" | "OFFLINE";
  currentTaskCount: number;
  maxConcurrentTasks: number;
  utilizationRate: number;
}

interface AgentDashboardProps {
  selectedAgentId?: string;
  onAgentChange?: (agentId: string) => void;
}

export function AgentDashboard({
  selectedAgentId,
  onAgentChange,
}: AgentDashboardProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [currentAvailability, setCurrentAvailability] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  // Fetch agents
  const fetchAgents = useCallback(async () => {
    try {
      const response = await fetch("/api/agents");
      const result = await response.json();

      if (result.success) {
        setAgents(result.data.agents);

        // Auto-select agent based on selectedAgentId or first agent
        if (selectedAgentId) {
          const agent = result.data.agents.find(
            (a: Agent) => a.id === selectedAgentId
          );
          if (agent) {
            setSelectedAgent(agent);
            if (onAgentChange) onAgentChange(agent.id);
          }
        } else if (result.data.agents.length > 0) {
          const firstAgent = result.data.agents[0];
          setSelectedAgent(firstAgent);
          if (onAgentChange) onAgentChange(firstAgent.id);
        }
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
      toast.error("Failed to load agents");
    }
  }, [selectedAgentId, onAgentChange]);

  // Fetch tasks for selected agent
  const fetchTasks = async (agentId: string) => {
    try {
      const response = await fetch(`/api/tasks?assignedTo=${agentId}`);
      const result = await response.json();

      if (result.success) {
        setTasks(result.data.tasks);
      }
    } catch (error) {
      console.error("Error fetching tasks:", error);
      toast.error("Failed to load tasks");
    }
  };

  // Fetch current availability status
  const fetchAvailability = async (agentId: string) => {
    try {
      const response = await fetch(`/api/agents/${agentId}/availability`);
      const result = await response.json();

      if (result.success && result.data.availability) {
        setCurrentAvailability(result.data.availability.status);
      } else {
        setCurrentAvailability('available'); // Default to available if no status set
      }
    } catch (error) {
      console.error("Error fetching availability:", error);
      setCurrentAvailability('available'); // Default to available on error
    }
  };

  // Update task status with optimistic UI
  const updateTaskStatus = async (taskId: string, newStatus: string) => {
    // Optimistic update
    const originalTasks = [...tasks];
    setTasks(prevTasks =>
      prevTasks.map(task =>
        task.id === taskId ? { ...task, status: newStatus as Task["status"] } : task
      )
    );

    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Task status updated");
        // Refresh data to ensure consistency
        if (selectedAgent) {
          fetchTasks(selectedAgent.id);
          fetchAgents(); // Refresh agent data
        }
      } else {
        // Revert optimistic update on error
        setTasks(originalTasks);
        throw new Error(result.error?.message || "Failed to update task");
      }
    } catch (error) {
      // Revert optimistic update on error
      setTasks(originalTasks);
      console.error("Error updating task:", error);
      toast.error("Failed to update task status");
    }
  };

  // Update agent status
  const updateAgentStatus = async (agentId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/agents/${agentId}/status`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Status updated to ${newStatus}`);
        fetchAgents();
      } else {
        throw new Error(result.error?.message || "Failed to update status");
      }
    } catch (error) {
      console.error("Error updating agent status:", error);
      toast.error("Failed to update agent status");
    }
  };

  useEffect(() => {
    fetchAgents();
    setLoading(false);
  }, [fetchAgents]);

  useEffect(() => {
    if (selectedAgent) {
      fetchTasks(selectedAgent.id);
      fetchAvailability(selectedAgent.id);
    }
  }, [selectedAgent]);

  // Polling for real-time updates
  useEffect(() => {
    if (!selectedAgent || isPolling) return;

    const pollInterval = setInterval(() => {
      setIsPolling(true);
      Promise.all([
        fetchTasks(selectedAgent.id),
        fetchAgents()
      ]).finally(() => {
        setIsPolling(false);
      });
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(pollInterval);
  }, [selectedAgent, isPolling, fetchAgents]);

  const handleAgentSelect = (agentId: string) => {
    const agent = agents.find((a) => a.id === agentId);
    if (agent) {
      setSelectedAgent(agent);
      if (onAgentChange) onAgentChange(agentId);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENT":
        return "bg-red-500 text-white";
      case "HIGH":
        return "bg-orange-500 text-white";
      case "MEDIUM":
        return "bg-yellow-500 text-white";
      case "LOW":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "bg-green-500 text-white";
      case "IN_PROGRESS":
        return "bg-blue-500 text-white";
      case "ASSIGNED":
        return "bg-purple-500 text-white";
      case "PENDING":
        return "bg-yellow-500 text-white";
      case "ESCALATED":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case "AVAILABLE":
        return "bg-green-500";
      case "BUSY":
        return "bg-red-500";
      case "AWAY":
        return "bg-yellow-500";
      case "OFFLINE":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get the display status (prioritize availability status over user status)
  const getDisplayStatus = () => {
    if (currentAvailability) {
      return currentAvailability.toUpperCase();
    }
    return selectedAgent?.status || 'AVAILABLE';
  };

  // Filter tasks
  const filteredTasks = tasks.filter((task) => {
    if (statusFilter !== "all" && task.status !== statusFilter) return false;
    if (priorityFilter !== "all" && task.priority !== priorityFilter)
      return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Agent Selection - Only show if no specific agent is selected */}
      {!selectedAgentId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Agent Dashboard
            </CardTitle>
            <CardDescription>
              View and manage tasks for individual agents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Select
                  value={selectedAgent?.id || ""}
                  onValueChange={handleAgentSelect}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an agent" />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        <div className="flex items-center gap-2">
                          <div
                            className={`w-2 h-2 rounded-full ${getAgentStatusColor(
                              agent.status
                            )}`}
                          />
                          {agent.name} ({agent.currentTaskCount}/
                          {agent.maxConcurrentTasks})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" size="sm" onClick={() => fetchAgents()}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {selectedAgent && (
        <>
          {/* Agent Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{selectedAgent.name}</span>
                <div className="flex items-center gap-2">
                  <Badge
                    className={`${getAgentStatusColor(
                      getDisplayStatus()
                    )} text-white`}
                  >
                    {getDisplayStatus()}
                  </Badge>
                  <Select
                    value={selectedAgent.status}
                    onValueChange={(value) =>
                      updateAgentStatus(selectedAgent.id, value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AVAILABLE">Available</SelectItem>
                      <SelectItem value="BUSY">Busy</SelectItem>
                      <SelectItem value="AWAY">Away</SelectItem>
                      <SelectItem value="OFFLINE">Offline</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardTitle>
              <CardDescription>{selectedAgent.email}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {selectedAgent.currentTaskCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Current Tasks
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {selectedAgent.maxConcurrentTasks}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Max Capacity
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {selectedAgent.utilizationRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Utilization
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <Progress
                  value={selectedAgent.utilizationRate}
                  className="h-2"
                />
              </div>
            </CardContent>
          </Card>

          {/* Agent Management Tabs */}
          <Tabs defaultValue="kanban" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="kanban" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Kanban
              </TabsTrigger>
              <TabsTrigger value="tasks" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Tasks ({filteredTasks.length})
              </TabsTrigger>
              <TabsTrigger value="schedule" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Working Hours
              </TabsTrigger>
              <TabsTrigger value="availability" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Availability
              </TabsTrigger>
            </TabsList>

            <TabsContent value="kanban" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">Task Board</h3>
                  {isPolling && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      Syncing...
                    </div>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectedAgent && fetchTasks(selectedAgent.id)}
                  disabled={isPolling}
                >
                  <RefreshCw className={`h-4 w-4 ${isPolling ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
              <AgentKanbanBoard
                tasks={tasks}
                onStatusChange={updateTaskStatus}
              />
            </TabsContent>

            <TabsContent value="tasks" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Tasks ({filteredTasks.length})</span>
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="PENDING">Pending</SelectItem>
                          <SelectItem value="ASSIGNED">Assigned</SelectItem>
                          <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                          <SelectItem value="COMPLETED">Completed</SelectItem>
                          <SelectItem value="ESCALATED">Escalated</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select
                        value={priorityFilter}
                        onValueChange={setPriorityFilter}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Priority</SelectItem>
                          <SelectItem value="LOW">Low</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HIGH">High</SelectItem>
                          <SelectItem value="URGENT">Urgent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {filteredTasks.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No tasks found for the selected filters
                      </div>
                    ) : (
                      filteredTasks.map((task) => (
                        <div key={task.id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium">{task.title}</h4>
                              {task.description && (
                                <p className="text-sm text-muted-foreground mt-1">
                                  {task.description}
                                </p>
                              )}
                              <div className="flex items-center gap-2 mt-2">
                                <Badge className={getPriorityColor(task.priority)}>
                                  {task.priority}
                                </Badge>
                                <Badge variant="outline">{task.type}</Badge>
                                <span className="text-xs text-muted-foreground">
                                  Created{" "}
                                  {new Date(task.createdAt).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={getStatusColor(task.status)}>
                                {task.status.replace("_", " ")}
                              </Badge>
                              {task.status === "ASSIGNED" && (
                                <Button
                                  size="sm"
                                  onClick={() =>
                                    updateTaskStatus(task.id, "IN_PROGRESS")
                                  }
                                >
                                  Start
                                </Button>
                              )}
                              {task.status === "IN_PROGRESS" && (
                                <Button
                                  size="sm"
                                  onClick={() =>
                                    updateTaskStatus(task.id, "COMPLETED")
                                  }
                                >
                                  Complete
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="schedule" className="space-y-4">
              <WorkingHoursManager agentId={selectedAgent.id} />
            </TabsContent>

            <TabsContent value="availability" className="space-y-4">
              <AvailabilityManager
                agentId={selectedAgent.id}
                currentStatus={currentAvailability || selectedAgent.status}
                onStatusChange={(status) => {
                  setCurrentAvailability(status);
                  fetchAgents(); // Refresh agent data
                  fetchAvailability(selectedAgent.id); // Refresh availability status
                }}
              />
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
